<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        Network Button Test Page
      </h1>
      
      <!-- 认证状态 -->
      <div class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-2">Authentication Status</h2>
        <p v-if="currentUser" class="text-green-600">
          ✅ Logged in as: {{ currentUser.email }}
        </p>
        <p v-else class="text-red-600">
          ❌ Not logged in
        </p>
      </div>

      <!-- 测试数据选择 -->
      <div class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">Test Data Selection</h2>
        <div class="space-y-4">
          <button
            @click="loadGitHubTestData"
            class="w-full p-3 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Load GitHub Test Data
          </button>
          <button
            @click="loadScholarTestData"
            class="w-full p-3 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Load Scholar Test Data
          </button>
          <button
            @click="loadCompanyTestData"
            class="w-full p-3 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Load Company Test Data
          </button>
        </div>
      </div>

      <!-- 当前测试数据显示 -->
      <div v-if="testCandidate" class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">Current Test Data</h2>
        <pre class="bg-gray-100 dark:bg-gray-700 p-3 rounded text-sm overflow-auto">{{ JSON.stringify(testCandidate, null, 2) }}</pre>
      </div>

      <!-- Network按钮测试 -->
      <div v-if="testCandidate" class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">Network Button Test</h2>
        <button
          @click="showNetworkModal = true"
          class="px-6 py-3 bg-indigo-500 text-white rounded hover:bg-indigo-600"
        >
          Test Network Button
        </button>
      </div>

      <!-- API调用日志 -->
      <div v-if="apiLogs.length > 0" class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">API Call Logs</h2>
        <div class="space-y-2 max-h-64 overflow-auto">
          <div
            v-for="(log, index) in apiLogs"
            :key="index"
            class="p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm"
          >
            <div class="font-mono">{{ log.timestamp }}</div>
            <div class="font-semibold" :class="log.success ? 'text-green-600' : 'text-red-600'">
              {{ log.success ? '✅' : '❌' }} {{ log.message }}
            </div>
            <div v-if="log.details" class="text-gray-600 dark:text-gray-400 mt-1">
              {{ log.details }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Network Modal -->
    <NetworkModal
      v-if="testCandidate"
      :show="showNetworkModal"
      :currentCandidate="testCandidate"
      @update:show="showNetworkModal = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NetworkModal from '~/components/TalentCard/index.vue'

// 认证状态
const { currentUser } = useFirebaseAuth()

// 测试状态
const testCandidate = ref(null)
const showNetworkModal = ref(false)
const apiLogs = ref([])

// 添加API调用日志
const addLog = (message: string, success: boolean = true, details?: string) => {
  apiLogs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    success,
    details
  })
  // 只保留最近20条日志
  if (apiLogs.value.length > 20) {
    apiLogs.value = apiLogs.value.slice(0, 20)
  }
}

// 监听Network API调用（通过拦截fetch）
if (process.client) {
  const originalFetch = window.fetch
  window.fetch = async (...args) => {
    const [url, options] = args
    
    // 检查是否是Network API调用
    if (typeof url === 'string' && url.includes('/api/v1/talent/network')) {
      addLog(`Network API called: ${url}`, true, `Method: ${options?.method || 'GET'}`)
    }
    
    try {
      const response = await originalFetch(...args)
      
      if (typeof url === 'string' && url.includes('/api/v1/talent/network')) {
        if (response.ok) {
          addLog(`Network API success: ${response.status}`, true)
        } else {
          addLog(`Network API failed: ${response.status}`, false, await response.text())
        }
      }
      
      return response
    } catch (error) {
      if (typeof url === 'string' && url.includes('/api/v1/talent/network')) {
        addLog(`Network API error: ${error.message}`, false)
      }
      throw error
    }
  }
}

// 加载GitHub测试数据
const loadGitHubTestData = () => {
  testCandidate.value = {
    builder: 'github',
    group: 'default',
    data: {
      login: 'DateBro',
      name: 'Zhiyong(Johnny) Zhao',
      id: '7e060849-ffd9-4e55-bc22-93b6bfd65b0d',
      bio: 'Incoming SWE @google',
      avatar_url: 'https://avatars.githubusercontent.com/u/32711044?u=22872c461667275608910ffc4e193183e1077e5a&v=4'
    },
    profile: {
      name: 'Zhiyong(Johnny) Zhao',
      id: 'de268057-f3bf-4684-a541-b1d6c8cfe67a',
      github: 'DateBro',
      openreview: null,
      avatar_url: 'https://avatars.githubusercontent.com/u/32711044?u=22872c461667275608910ffc4e193183e1077e5a&v=4'
    },
    // SearchCard显示数据
    id: 'DateBro',
    name: 'Zhiyong(Johnny) Zhao',
    positionTitle: 'Software Engineer',
    institution: 'Google',
    avatarUrl: 'https://avatars.githubusercontent.com/u/32711044?u=22872c461667275608910ffc4e193183e1077e5a&v=4',
    skills: ['computer-science', 'security', 'machine-learning'],
    featuredWork: {
      title: 'Featured Repository',
      venue: 'GitHub',
      type: 'Repository',
      year: '2024'
    },
    recommendations: ['Strong GitHub contributions'],
    matchScore: 85
  }
  addLog('Loaded GitHub test data', true, 'ID will be extracted from profile.github')
}

// 加载Scholar测试数据
const loadScholarTestData = () => {
  testCandidate.value = {
    builder: 'scholar',
    group: 'default',
    data: {
      id: 'scholar-data-id',
      name: 'Eric Wallace',
      openreview: '~Eric_Wallace1'
    },
    profile: {
      name: 'Eric Wallace',
      id: 'profile-id-123',
      github: null,
      openreview: '~Eric_Wallace1',
      avatar_url: 'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=SgST3LkAAAAJ'
    },
    // SearchCard显示数据
    id: '~Eric_Wallace1',
    name: 'Eric Wallace',
    positionTitle: 'Researcher',
    institution: 'OpenAI',
    avatarUrl: 'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=SgST3LkAAAAJ',
    skills: ['LLM', 'ML', 'NLP'],
    featuredWork: {
      title: 'Predicting Emergent Capabilities by Finetuning',
      venue: 'COLM',
      type: 'Poster',
      year: '2024'
    },
    recommendations: ['Leading researcher in LLM'],
    matchScore: 92
  }
  addLog('Loaded Scholar test data', true, 'ID will be extracted from profile.openreview')
}

// 加载Company测试数据
const loadCompanyTestData = () => {
  testCandidate.value = {
    builder: 'scholar',
    group: 'company',
    data: {
      id: 'data-id-456',
      name: 'Ziad Reslan',
      openreview: null
    },
    profile: {
      name: 'Ziad Reslan',
      id: '156de05c-806a-4da8-b619-72cf74a61b0c',
      github: null,
      openreview: null,
      company: 'OpenAI',
      avatar_url: 'https://scholar.google.com/citations/images/avatar_scholar_56.png'
    },
    // SearchCard显示数据
    id: '156de05c-806a-4da8-b619-72cf74a61b0c',
    name: 'Ziad Reslan',
    positionTitle: 'Researcher',
    institution: 'OpenAI',
    avatarUrl: 'https://scholar.google.com/citations/images/avatar_scholar_56.png',
    skills: ['LLM'],
    featuredWork: {
      title: "Google's Evolving Approaches to Countering Hate Speech",
      venue: 'Research',
      type: 'Published',
      year: '2020'
    },
    recommendations: ['Company researcher'],
    matchScore: 78
  }
  addLog('Loaded Company test data', true, 'ID will be extracted from profile.id with group=company')
}
</script>

<style scoped>
/* 简单的样式 */
</style>
